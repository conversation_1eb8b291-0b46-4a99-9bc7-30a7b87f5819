{"app": {"title": "Dairy Ration Formulation Platform"}, "nav": {"dashboard": "Dashboard", "feeds": "Feeds", "herds": "Herds", "rations": "Rations", "animalGroups": "Animal Groups", "logout": "Logout", "knowledgeBase": "Knowledge Base"}, "dashboard": {"welcome": "Welcome to the Dairy Ration Formulation Platform", "feeds": "Feeds", "herds": "Herds", "rations": "Rations", "milkProduction": "Milk Production", "bcs": "Body Condition Score"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "noAccount": "Don't have an account?", "haveAccount": "Already have an account?", "signUp": "Sign Up", "signIn": "Sign In", "name": "Name", "editPassword": "Edit Password", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "passwordChanged": "Password changed successfully", "currentPasswordIncorrect": "Current password is incorrect", "passwordMismatch": "New passwords do not match"}, "common": {"add": "Add", "refresh": "Refresh", "reset": "Reset", "create": "Create", "edit": "Edit", "delete": "Delete", "save": "Save", "saveAndFinish": "Save and Finish", "success": "Success", "currencySymbol": "$", "cancel": "Cancel", "back": "Back", "next": "Next", "submit": "Submit", "loading": "Loading...", "search": "Search", "filter": "Filter", "sort": "Sort", "actions": "Actions", "name": "Name", "description": "Description", "date": "Date", "status": "Status", "type": "Type", "value": "Value", "required": "Required", "optional": "Optional", "selectOption": "Select an option", "requiredMark": "*", "tryAgain": "Try Again", "created": "Created", "public": "Public", "private": "Private", "viewAll": "View all", "showLess": "Show Less", "showMoreDetails": "Show More Details", "yes": "Yes", "no": "No", "saving": "Saving...", "searching": "Searching...", "thinking": "Thinking...", "view": "View", "published": "Published", "draft": "Draft", "submitting": "Submitting...", "previous": "Previous", "page": "Page", "of": "of", "total": "Total", "refreshing": "Refreshing...", "hideInfo": "Hide Info", "showInfo": "Show Info", "update": "Update", "errorOccurred": "An error occurred. Please try again.", "basicInformation": "Basic Information", "items": "items", "clearSearch": "Clear Search", "comingSoon": "Coming Soon", "unsavedChanges": "You have unsaved changes", "added": "Added", "saveAsDraft": "Save as Draft", "remove": "remove", "error": "error"}, "feeds": {"title": "Feeds", "createNew": "Create New Feed", "editFeed": "Edit Feed", "feedDetails": "Feed Details", "feedName": "Feed Name", "feedType": "Feed Type", "dryMatter": "Dry Matter", "protein": "<PERSON><PERSON>", "energy": "Energy", "fiber": "Fiber", "minerals": "Minerals", "vitamins": "Vitamins", "fat": "Fat", "calcium": "Calcium", "phosphorus": "Phosphorus", "cost": "Cost", "recentlyAdded": "Your most recently added feeds", "noFeedsFound": "No feeds found", "library": "Feeds Library", "addNew": "Add New Feed", "errorLoading": "Error loading feeds", "noFeedsFoundLong": "No feeds found. Add your first feed to get started.", "dmShort": "DM", "costShort": "Cost", "backToFeeds": "Back to Feeds", "feedNotFound": "Feed not found", "feedInformation": "Feed Information", "costPerKg": "Cost per kg", "visibility": "Visibility", "nutrientComposition": "Nutrient Composition", "noNutrientData": "No nutrient data available", "confirmDeletion": "Confirm Deletion", "deleteConfirmationMessage": "Are you sure you want to delete {{name}}? This action cannot be undone.", "dryMatterPercent": "Dry Matter (%)", "costPerKgCurrency": "Cost per kg ($)", "usdEquivalent": "USD equivalent", "makePublic": "Make this feed public (visible to all users)", "nutrientName": "Nutrient Name", "unit": "Unit", "noNutrientsAdded": "No nutrients added yet", "addNutrient": "Add Nutrient", "saveFeed": "Save Feed", "dryMatterRequired": "Dry matter percentage is required", "dryMatterRange": "Dry matter percentage must be between 0 and 100", "costRequired": "Cost per kg is required", "costPositive": "Cost per kg must be a positive number", "nutrientNameRequired": "Nutrient name is required", "valueRequired": "Value is required", "valueMustBeNumber": "Value must be a number", "totalFeeds": "feeds in total", "search": "Search feeds...", "noMatchingFeeds": "No matching feeds found", "noFeedsAvailable": "No feeds available", "totalAvailable": "Total Available", "selected": "Selected"}, "herds": {"title": "Herds", "createNew": "Create <PERSON> Herd", "editHerd": "<PERSON>", "herdDetails": "Herd Details", "herdName": "Herd Name", "animalType": "Animal Type", "count": "Count", "averageWeight": "Average Weight", "productionLevel": "Production Level", "ageGroup": "Age Group", "addNew": "Add New Herd", "errorLoading": "Error loading herds", "noHerdsFoundLong": "No herds found. Add your first herd to get started.", "groups": "Groups", "backToHerds": "Back to Herds", "herdNotFound": "Herd not found", "earNumber": "Ear Number", "basicInformation": "Basic Information", "breed": "Breed", "gender": "Gender", "color": "Color", "birthWeight": "Birth Weight", "birthDate": "Birth Date", "ageMonths": "Age (Months)", "fatherEarNum": "Father E<PERSON> #", "unnamed": "Unnamed Animal", "born": "Born", "healthy": "Healthy", "sick": "Sick", "notOnFarm": "Not on Farm", "motherEarNum": "Mother Ear #", "productionStatus": "Production & Status", "lactationNumber": "Lactation Number", "group": "Group", "growthStatus": "Growth Status", "fertilityStatus": "Fertility Status", "healthStatus": "Health Status", "disease": "Disease", "height": "Height", "bust": "Bust", "entryExitInfo": "Entry & Exit Information", "entryDate": "Entry Date", "entryType": "Entry Type", "onFarm": "On Farm", "exitDate": "Exit Date", "exitType": "Exit Type", "exitReason": "Exit Reason", "importantDates": "Important Dates", "measureDate": "Measure Date", "calvingDate": "<PERSON><PERSON>", "dryDate": "Dry Date", "inseminationDate": "Insemination Date", "abortionDate": "Abortion Date", "cureDate": "Cure Date", "remarks": "Remarks", "confirmDeletion": "Confirm Deletion", "deleteConfirmationMessage": "Are you sure you want to delete {{name}}? This action cannot be undone.", "animalDetails": "Animal Details", "birthWeightKg": "Birth Weight (kg)", "datesStatus": "Dates & Status", "groupName": "Group Name", "additionalDates": "Additional Dates", "isOnFarm": "Is On Farm (1=Yes, 0=No)", "healthNotes": "Health & Notes", "saveHerd": "Save Herd", "updateHerd": "Update Herd", "male": "Male", "female": "Female", "searchByEarNumber": "Search by ear number, name or breed...", "animalsFound": "animals found", "noSearchResults": "No animals found matching your search criteria", "viewAnimal": "View animal #{{id}} {{name}}", "viewUnnamedAnimal": "View unnamed animal #{{id}}"}, "rations": {"title": "Rations", "createNew": "Create New Ration", "editRation": "<PERSON> Ration", "rationDetails": "Ration Details", "rationName": "Ration Name", "description": "Description", "targetHerd": "Target Herd", "ingredients": "Ingredients", "nutritionalValues": "Nutritional Values", "formulate": "Formulate", "optimize": "Optimize", "errorLoading": "Error loading rations", "noRationsFoundLong": "No rations found. Create your first ration to get started.", "formulated": "Formulated", "notFormulated": "Not Formulated", "reformulate": "Reformulate", "recent": "Recent Rations", "recentlyFormulated": "Your most recently formulated rations", "noRationsFound": "No rations found", "totalRations": "rations in total", "lastFormulated": "Last formulated", "notYetFormulated": "Not yet formulated", "pending": "Pending", "backToRations": "Back to Rations", "rationNotFound": "Ration not found", "feedIngredients": "Feed Ingredients", "min": "Min", "max": "Max", "noMin": "no min", "noMax": "no max", "none": "None", "noFeedsAdded": "No feeds added to this ration", "nutritionalConstraints": "Nutritional Constraints", "noConstraintsDefined": "No nutritional constraints defined", "nrcRequirementsAvailable": "NRC model available", "readyToFormulate": "Ready to formulate", "readyToFormulateDescription": "Click formulate ration to start formulation", "constraintsMet": "Constraints met", "aiAssistant": "AI Assistant", "aiAssistance": "AI Assistance", "askAIPlaceholder": "Ask the AI assistant about this ration...", "aiAssistantHint": "Try asking for help with formulation, feed suggestions, or nutritional advice.", "aiCommands": {"formulate": "Please formulate the current ration."}, "startFormulation": "Start Formulation", "additionalInstructions": "Additional Instructions (Optional)", "instructionsPlaceholder": "Enter any specific requirements or constraints for this formulation...", "proceedToFormulation": "Proceed to Formulation", "aiTabs": {"conversation": "Conversation", "changes": "Changes", "analysis": "Analysis"}, "noChangesToApply": "No changes made by AI to apply.", "noChangesToDisplay": "No changes to display", "changesDetected": "Changes Detected", "aiWelcomeMessage": "Hello! I'm your AI ration formulation assistant. How can I help you optimize this ration?", "aiInitializationError": "There was an error initializing the AI assistant. Please close and try again.", "aiProcessingError": "Sorry, there was an error processing your request.", "aiActionTaken": "AI Action Taken", "recommendations": "Recommendations", "noRecommendations": "No recommendations", "noRecommendationsYet": "No recommendations generated yet.", "actionHistory": "Action History", "noActionsYet": "No AI actions taken yet.", "aiActionPrompt": "Triggered by your message", "changesReady": "Changes are ready to be applied.", "applyChanges": "Apply Changes", "errorApplyingChanges": "Error applying AI changes.", "aiChangesApplied": "AI changes applied successfully!", "noChangesYet": "No changes have been made by the AI assistant yet.", "feedChanges": "Feed Changes", "noFeedChanges": "No feed changes", "addedFeeds": "Added Feeds", "removedFeeds": "Removed Feeds", "modifiedFeeds": "Modified Feeds", "inclusionRange": "Inclusion Range", "actual": "Actual", "before": "Before", "after": "After", "rangeChangedFrom": "Range changed from", "rangeChangedTo": "to", "constraintChanges": "Constraint Changes", "noConstraintChanges": "No constraint changes", "addedConstraints": "Added Constraints", "removedConstraints": "Removed Constraints", "modifiedConstraints": "Modified Constraints", "feedsUsed": "Feeds Used", "actionCompleted": "Action completed.", "formulationChanges": "Formulation Changes", "rationFormulated": "Ration formulated", "aiAnalysis": "AI Analysis", "formulationSummary": "Formulation Summary", "autoFormulating": "Auto-formulating...", "autoFormulationInProgress": "Automatic formulation in progress", "autoFormulationActive": "Automatic formulation is active", "autoFormulationHint": "The AI is automatically formulating your ration. You can stop the process at any time.", "formulationStopped": "Formulation process stopped", "aiCommunicationError": "Failed to communicate with AI service", "aiActionsSummary": "AI Actions Summary", "noAiActions": "No AI actions taken yet", "emptyAgentResponse": "Sorry, I didn't get a valid response. Please try again.", "statusUpdates": {"thinking": "Thinking...", "autoFormulating": "Auto-formulating...", "autoFormulationInProgress": "Automatic formulation in progress", "autoFormulationActive": "Automatic formulation is active", "autoFormulationHint": "The AI is automatically formulating your ration. You can stop the process at any time.", "formulationStopped": "Formulation process stopped", "stopped": "Stopped", "continue": "Continue formulation process. Stop when complete or unresolvable."}, "aiActions": {"addFeed": "<PERSON><PERSON> Feed", "removeFeed": "Re<PERSON><PERSON>", "updateConstraint": "Update Constraint", "runFormulation": "Run Formulation", "saveFormulation": "Save Formulation", "analyzeFormulation": "<PERSON><PERSON><PERSON>", "getFeedInfo": "Get Feed Info", "searchFeeds": "Search Feeds", "getKnowledgeBase": "Search KB"}, "functionNames": {"add_feed_to_ration": "Add Feed to Ration", "remove_feed_from_ration": "<PERSON><PERSON>ve Feed from Ration", "update_nutrient_constraint": "Update Nutrient Constraint", "run_formulation": "Run Formulation", "save_formulation": "Save Formulation", "analyze_formulation_result": "Analyze Formulation Result", "get_feed_info": "Get Feed Information", "search_feeds": "Search Feeds", "get_knowledge_base": "Search Knowledge Base", "finish_formulation": "Finish Formulation", "check_nutrition": "Check Nutrition", "make_ration": "Make Ration", "add_nutrients_to_feed": "Add Nutrients to Feed"}, "functionResults": {"add_feed_to_ration": "Feed successfully added to the ration", "remove_feed_from_ration": "Feed successfully removed from the ration", "update_nutrient_constraint": "Nutrient constraint successfully updated", "run_formulation": "Formulation completed successfully", "save_formulation": "Ration saved successfully", "analyze_formulation_result": "Formulation analysis completed", "get_feed_info": "Feed information retrieved", "search_feeds": "Feed search completed", "get_knowledge_base": "Knowledge base search completed", "finish_formulation": "Formulation process finished", "check_nutrition": "Nutrition check completed", "make_ration": "Ration created successfully", "add_nutrients_to_feed": "Nutrients added to feed successfully"}, "toolMessages": {"addedFeedsToRation": "Added {{count}} feed(s) to the ration: {{feedNames}}", "removedFeedsFromRation": "Removed {{count}} feed(s) from the ration: {{feedNames}}", "updatedNutrientConstraints": "Updated {{count}} nutrient constraints: {{constraints}}", "addedNutrientConstraints": "Added {{count}} nutrient constraints: {{constraints}}", "updatedAndAddedNutrientConstraints": "Updated {{updateCount}} and added {{addCount}} nutrient constraints: {{constraints}}", "formulationSuccessful": "Formulation successful. Total cost: {{cost}} per day.", "formulationFailed": "Formulation failed: {{reason}}", "kbArticlesFound": "Found {{count}} relevant articles", "noKbArticlesFound": "No knowledge base articles found for query: '{{query}}'", "couldNotFindFeeds": "Could not find {{count}} feed(s)", "rationFormulationCreated": "Ration formulation created and saved successfully"}, "checkNutrition": {"completed": "Nutrition check completed.", "requirementsNotMet": "Some requirements were not met.", "dmiMismatch": "DMI target mismatch detected."}, "conflictsDetected": "{{count}} potential conflict(s) detected", "confirmDeletion": "Confirm Deletion", "deleteConfirmationMessage": "Are you sure you want to delete {{name}}? This action cannot be undone.", "herd": "Herd", "animalGroup": "Animal Group", "selectHerd": "Select Herd", "selectGroup": "Select Group", "selectHerdFirst": "Select Herd First", "feed": "Feed", "minimumInclusion": "Minimum Inclusion (%)", "maximumInclusion": "Maximum Inclusion (%)", "addFeed": "<PERSON><PERSON> Feed", "addFeedsToLibraryFirst": "Please add feeds to your library first", "nutrientName": "Nutrient Name", "nutrientResults": "nutrient profile", "minimumValue": "Minimum Value", "maximumValue": "Maximum Value", "noConstraintsAdded": "No constraints added yet", "addConstraint": "Add Constraint", "saveRation": "Save Ration", "animalGroupRequired": "Animal group is required", "nutrientNameRequired": "Nutrient name is required", "formulateRation": "Formulate Ration", "cannotFormulate": "Cannot formulate ration", "addFeedsBeforeFormulating": "Please add feeds to this ration before formulating.", "formulation": "Formulation", "formulationResult": "Formulation Result", "formulationResults": "Formulation Results", "noFormulationResults": "No formulation results available", "totalCost": "Total Cost", "viewRationDetails": "View Ration Details", "formulationError": "Formulation Error", "formulationErrorMessage": "Failed to formulate ration. Please check your constraints and try again.", "backToRation": "Back to Ration", "formulating": "Formulating...", "usingNrcRequirements": "Using NRC 8th Edition requirements for this animal group", "availableFeeds": "Available Feeds", "search": "Search feeds...", "noMatchingFeeds": "No matching feeds found", "noFeedsAvailable": "No feeds available", "added": "Added", "addFeedsHint": "Select feeds from the list to add to your ration", "resetToNrc": "Reset to NRC Requirements", "addDefaultConstraints": "Add Default Constraints", "completeBasicInfoFirst": "Please complete the basic information first", "selectAnimalGroup": "Select an animal group", "addFeeds": "Add feeds to the ration", "saveRationFirst": "Save the ration first", "formulationStatus": "Formulation Status", "partialSuccess": "Partially Successful", "formulationSuccess": "Formulation Successful", "someConstraintsNotMet": "Some constraints could not be met", "nutritionStatus": "Nutrition Status", "someNutrientsMissing": "Some nutrient requirements not met", "allRequirementsMet": "All requirements met", "nutrientsAnalyzed": "nutrients analyzed", "feedComposition": "Feed Composition", "nutrientAnalysis": "Nutrient Analysis", "waitingForFormulation": "Waiting for formulation...", "clickFormulateHint": "Click formulate to optimize your ration", "met": "Met", "notMet": "Not Met", "noFeedsIncluded": "No feeds included in the formulation", "detailedResults": "Detailed Results", "inclusion": "Inclusion %", "constraint": "Constraint", "cost": "Cost", "costAnalysis": "Cost Analysis", "perDayPerAnimal": "per day per animal", "basicInformation": "Basic Information", "noAnimalGroupsAvailable": "No animal groups available", "nutrientSummary": "Nutrient Summary", "noNutrientData": "No nutrient data available", "constraintsNote": "Note: Constraints define the minimum and maximum values for each nutrient in the ration", "nutritionalRequirements": "Nutritional Requirements", "selectedFeeds": "Selected Feeds", "noFeedsSelected": "No feeds selected", "createRation": "Create Ration", "saveAsDraft": "Save as Draft", "saveAndFinish": "Save and Finish", "selectAnimalGroupHint": "Select an animal group to see nutritional requirements", "clearConstraints": "Clear Constraints", "resetToModel": "Redo Model Requirements", "troubleshooting": "Troubleshooting", "troubleshootingTitle": "Formulation Troubleshooting", "troubleshootingDescription": "If you're having trouble with your ration formulation, check these common issues:", "checkConstraints": "Check your nutritional constraints", "checkConstraintsDescription": "Make sure your minimum and maximum values are realistic and achievable with the selected feeds.", "checkFeeds": "Check your feed selections", "checkFeedsDescription": "Ensure you have a good mix of feeds that can meet all nutritional requirements.", "checkInclusions": "Check feed inclusion limits", "checkInclusionsDescription": "Verify that feed minimum and maximum inclusion percentages allow enough flexibility.", "infeasibleFormulation": "Infeasible Formulation", "infeasibleFormulationDescription": "The current combination of feeds and constraints cannot produce a valid solution.", "formulationFailed": "Formulation Failed", "formulationFailedDescription": "The optimization solver encountered an error. Try adjusting your constraints or feeds.", "tryAgain": "Try Again", "adjustAndRetry": "Adjust Settings & Retry", "formulationIncomplete": "Formulation Incomplete", "formulationIncompleteDescription": "Your ration formulation is incomplete. Please address the issues below:", "conflictingConstraints": "Conflicting Constraints", "conflictingConstraintsDescription": "Some of your nutritional constraints may be in conflict with each other.", "insufficientFeedVariety": "Insufficient Feed Variety", "insufficientFeedVarietyDescription": "Add more variety of feeds to meet all nutritional requirements.", "tooRestrictive": "Constraints Too Restrictive", "tooRestrictiveDescription": "Your constraints may be too tight. Try relaxing some minimum or maximum values.", "atMinimumInclusion": "At Minimum Inclusion", "atMaximumInclusion": "At Maximum Inclusion", "requirementNotMet": "Requirement Not Met", "requirementMet": "Requirement Met", "actualValue": "Actual", "nutrient": "Nutrient", "status": "Status", "costPerKgDM": "Cost per kg DM", "perKgDryMatter": "per kg dry matter", "noFeasibleSolution": "No feasible solution exists with the given constraints.", "solverFailed": "The optimization solver encountered an error. Please try again or adjust your constraints.", "generalInfeasibility": "General Infeasibility", "feedMinimums": "Feed Minimums", "feedMinMaxConflict": "Feed Min/Max Conflict", "nutrientMinMaxConflict": "Nutrient Min/Max Conflict", "constraintsTooRestrictive": "The combination of constraints might be too restrictive. Consider relaxing nutrient requirements or feed inclusion limits.", "totalMinimumExceeds": "The total minimum inclusion exceeds 100%. Please reduce the minimum inclusion percentages.", "minGreaterThanMax": "Minimum inclusion is greater than maximum inclusion. Please adjust these values.", "specificTips": "Specific Recommendations", "definiteConflicts": "Definite Conflicts", "potentialConflicts": "Potential Issues", "potentialIssue": "Potential Issue", "nutrientMinimumUnachievable": "Nutrient Minimum Unachievable", "nutrientMaximumUnachievable": "Nutrient Maximum Unachievable", "nutrientMinDetail": "Minimum requirement of {value} {unit} for {nutrient} might be unachievable. Highest concentration in available feeds is {maxConc} {unit}.", "nutrientMaxDetail": "Maximum requirement of {value} {unit} for {nutrient} might be unachievable. Lowest concentration in available feeds is {minConc} {unit}.", "feedInclusionConflict": "Feed inclusion limits are incompatible. Total minimum inclusion exceeds 100%.", "feedMinMaxConflictDetail": "For feed '{feed}': Minimum inclusion ({min}%) is greater than maximum inclusion ({max}%).", "nutrientMinMaxConflictDetail": "For nutrient '{nutrient}': Minimum requirement ({min} {unit}) is greater than maximum requirement ({max} {unit})."}, "language": {"en": "English", "zh": "中文"}, "nrc": {"modelVersion": "NRC Model Version", "activeModel": "active selection", "selectModelVersion": "Select NRC Model Version", "useDefaultModel": "Use Default Model", "selectModel": "Select Model", "loadingModels": "Loading models...", "selectedModel": "Selected Model", "noDescriptionAvailable": "No description available", "modelDescription": "Different NRC models provide varying nutrient requirements based on research. The default model uses the latest recommendations."}, "optimization": {"title": "Optimization", "settings": "Optimization Settings", "objective": "Optimization Objective", "minimizeCost": "Minimize Cost", "maximizeMilkProduction": "Maximize Milk Production", "maximizeProfit": "Maximize Profit", "balanceNutrients": "Balance Nutrients", "showAdvancedSettings": "Show Advanced Settings", "maxIterations": "Maximum Iterations", "convergenceTolerance": "Convergence Tolerance", "currentSettingsDescription": "Current optimization objective: {{objective}}", "applySettings": "Apply Settings", "optimizing": "Optimizing...", "status": "Optimization Status", "readyToFormulate": "Ready to Formulate", "clickToStartDescription": "Click the button below to start the formulation process", "optimizationNote": "Optimization will find the best combination of feeds to meet your nutritional constraints at the lowest cost."}, "validation": {"nameRequired": "Name is required", "emailRequired": "Email is required", "emailInvalid": "<PERSON><PERSON> is invalid", "passwordRequired": "Password is required", "passwordLength": "Password must be at least 6 characters", "confirmPasswordRequired": "Please confirm your password", "passwordsDoNotMatch": "Passwords do not match", "mustBeNumber": "Must be a number", "minNotGreaterThanMax": "Min cannot be greater than max", "multipleErrors": "Multiple issues need to be fixed before proceeding", "required": "Required Field"}, "animalGroups": {"title": "Animal Groups", "addNew": "Add New Animal Group", "recentlyAdded": "Your most recently added animal groups", "editGroup": "Edit Animal Group", "groupName": "Group Name", "groupDetails": "Group Details", "details": "Details", "animals": "Animals", "avgWeight": "Average Weight", "avgLactation": "Average Lactation", "milkProduction": "Milk Production", "dailyMilkProduction": "Daily Milk Production (kg)", "daysInMilk": "Days in Milk", "avgBCS": "Average BCS", "bodyConditionScore": "Body Condition Score (1-5)", "lactationNumber": "Lactation Number", "animalsInGroup": "Animals in Group ({{count}})", "noAnimalsInGroup": "No animals in this group.", "backToGroups": "Back to Animal Groups", "confirmDeletion": "Confirm Deletion", "deleteConfirmationMessage": "Are you sure you want to delete {{name}}? This action cannot be undone.", "errorLoading": "Error loading animal groups", "groupNotFound": "Animal group not found", "searchGroups": "Search animal groups...", "noGroupsFound": "No animal groups found. Add your first animal group to get started.", "noGroupsFoundForSearch": "No animal groups found matching your search criteria.", "createFirst": "Create First Animal Group", "assignAnimals": "Assign Animals", "assignAnimalsDescription": "Select animals to add to this group.", "noAnimalsToAssign": "No animals to assign. Please add animals first.", "saveGroup": "Save Animal Group", "updateGroup": "Update Animal Group", "basicInformation": "Basic Information", "nrcRequirements": "NRC 8th Edition Requirements", "noNrcData": "Insufficient data to calculate NRC requirements. Please add weight and other animal data.", "dryMatterIntake": "Dry Matter Intake", "estimatedDMI": "Estimated DMI", "energyRequirements": "Energy Requirements", "maintenance": "Maintenance", "lactation": "Lactation", "pregnancy": "Pregnancy", "growth": "Growth", "totalEnergy": "Total Energy", "requiredConcentration": "Required Concentration", "proteinRequirements": "Protein Requirements", "totalProtein": "Total Protein", "fiberRequirements": "Fiber Requirements", "ndfRange": "NDF Range", "forageNdfMin": "Minimum Forage NDF", "adfMin": "Minimum ADF"}, "kb": {"title": "Knowledge Base", "description": "Find answers to common questions and learn about dairy ration formulation best practices.", "searchPlaceholder": "Search the knowledge base...", "searchResults": "Search Results for", "noSearchResults": "No results found for your search.", "viewAllResults": "View all results", "showingResults": "Showing {{count}} results", "resultsFound": "results found", "categories": "Categories", "selectCategory": "Please select", "noCategories": "No categories found", "articles": "Articles", "recentArticles": "Recent Articles", "popularArticles": "Popular Articles", "noArticles": "No articles found", "views": "views", "viewAllArticles": "View all articles", "popularTags": "Popular Tags", "noTags": "No tags found", "adminTitle": "Knowledge Base Admin", "adminDescription": "Manage your knowledge base content, categories, and embeddings.", "totalArticles": "Total Articles", "publishedArticles": "Published Articles", "totalCategories": "Total Categories", "totalViews": "Total Views", "createArticle": "Create Article", "editCategory": "Edit Category", "addCategory": "Add Category", "parentCategory": "Parent Category", "noParent": "No Parent", "createCategoryPrompt": "Create your first category to organize your knowledge base.", "confirmDeleteCategory": "Are you sure you want to delete this category? This action cannot be undone.", "confirmDeleteArticle": "Are you sure you want to delete this article? This action cannot be undone.", "kbtitle": "Title", "slug": "Slug", "category": "Category", "publishImmediately": "Publish Immediately", "tags": "Tags", "addTag": "Add a tag...", "content": "Content", "edit": "Edit", "preview": "Preview", "noContentToPreview": "No content to preview", "markdownSupported": "Markdown formatting is supported", "publishArticle": "Publish Article", "embeddingsStatus": "Embeddings Status", "embeddingsDescription": "Embeddings are vector representations of article content that enable semantic search. Here's the current status:", "completed": "Completed", "pending": "Pending", "failed": "Failed", "reprocessAll": "Reprocess All", "failedEmbeddings": "Failed Embeddings", "failedEmbeddingsDescription": "The following article chunks failed to generate embeddings. Click to regenerate them.", "regenerate": "Regenerate", "processingEmbeddings": "Processing Embeddings", "processingEmbeddingsDescription": "{{count}} embeddings are currently being processed. This may take a few minutes.", "askQuestion": "Have a Question?", "askQuestionDescription": "Use our AI assistant to ask questions and get answers from our knowledge base.", "askNow": "Ask Now", "presetQuestion1": "How do I formulate a dairy ration?", "presetQuestion2": "What are the main components of feed additives?", "presetQuestion3": "How can I improve milk production?", "home": "Home", "published": "Published", "updated": "Updated", "wasThisHelpful": "Was this article helpful?", "thankYouForPositiveFeedback": "Thank you for your feedback! We're glad this article was helpful.", "thankYouForFeedback": "Thank you for your feedback. We'll use it to improve our content.", "additionalComments": "Would you like to add any additional comments?", "howCanWeImprove": "How can we improve this article?", "feedbackPlaceholder": "Your comments here...", "relatedArticles": "Related Articles", "articleNotFound": "Article Not Found", "articleNotFoundDescription": "Sorry, the article you're looking for doesn't exist or has been moved.", "backToKnowledgeBase": "Back to Knowledge Base", "categoryNotFound": "Category Not Found", "categoryNotFoundDescription": "Sorry, the category you're looking for doesn't exist or has been moved.", "noCategoryArticles": "No articles found in this category.", "in": "in", "relevance": "Relevance", "askAIAssistant": "Ask AI Assistant", "askAIAssistantDescription": "Ask any question about dairy ration formulation and our AI will find the best answer from our knowledge base.", "askPlaceholder": "Ask a question...", "ask": "Ask", "thinkingAboutQuestion": "Thinking about your question...", "sources": "Sources", "hideSources": "Hide Sources", "showSources": "Show Sources", "minContext": "Minimal Context (1)", "defaultContext": "<PERSON><PERSON><PERSON> (3)", "maxContext": "Maximum Context (5)", "contextSizeExplanation": "Context size determines how many articles the AI will use to answer your question.", "askAnything": "Ask Anything About Dairy Ration Formulation", "askAnythingDescription": "Our AI assistant will search through our knowledge base to find the best answer to your question.", "errorProcessingQuestion": "There was an error processing your question. Please try again.", "enterSearchTerms": "Enter Search Terms", "searchDescription": "Search our knowledge base for articles, guides, and best practices.", "tryThese": "Try these searches", "startSearchTitle": "Search the Knowledge Base", "startSearchHint": "Enter a search term above to find articles in our knowledge base.", "search": "Search", "searchError": "An error occurred while searching. Please try again.", "tryDifferentSearch": "Try a different search term or browse our categories.", "searchMode": "Search Mode", "semanticSearch": "Semantic Search", "keywordSearch": "Keyword Search", "semanticSearchDescription": "Find conceptually similar content even if exact words don't match", "keywordSearchDescription": "Find exact word matches in content", "semanticSearchInfo": "About Semantic Search", "semanticSearchDetail": "Results are ranked by relevance to your query, even if they don't contain the exact words you searched for.", "searchInstead": "Search Instead", "manageArticles": "Manage Articles", "searchArticles": "Search articles...", "allArticles": "All Articles", "publishedOnly": "Published Only", "draftsOnly": "Drafts Only", "noArticlesFound": "No articles found matching your criteria.", "slugHelp": "URL-friendly version of the name (auto-generated, but can be edited)", "summary": "Summary", "summaryPlaceholder": "Brief summary of the article (optional)", "summaryHelp": "If not provided, a summary will be generated automatically.", "markdownHelp": "Use Markdown for formatting text, links, lists, code blocks, etc.", "saveDraft": "Save as Draft", "categoryRequired": "Please select a category", "articleCreatedSuccessfully": "Article created successfully!", "embeddings": "Embeddings", "embeddingsOverview": "Embeddings Overview", "processing": "Processing", "completedEmbeddings": "Completed Embeddings", "pendingEmbeddings": "Pending Embeddings", "embeddingsInfo": "About Embeddings", "embeddingsPending": "Pending: Articles waiting to be processed", "embeddingsProcessing": "Processing: Articles currently being embedded", "embeddingsCompleted": "Completed: Articles successfully embedded", "embeddingsFailed": "Failed: Articles that encountered errors during embedding", "reprocessFailedEmbeddings": "Reprocess Failed Embeddings", "reprocessDescription": "You can reprocess failed embeddings to try again. Select how many failed embeddings to reprocess at once.", "reprocessLimit": "Reprocess Limit", "reprocessSuccess": "Successfully queued {{count}} embeddings for reprocessing", "reprocessError": "An error occurred while reprocessing embeddings", "reembedCompleted": "Re-embed Completed", "reembedCompletedTooltip": "Use this when the embedding service has changed and you need to regenerate embeddings. This will re-chunk articles with the current settings.", "reembedSuccess": "Successfully queued {{count}} completed chunks for re-embedding", "processAllArticles": "Process all articles (may take longer)", "pendingJobs": "Pending Jobs", "pendingJobsDescription": "These are embeddings currently waiting to be processed or in progress.", "pendingJobsCount": "pending jobs", "processingJobsCount": "processing jobs", "jobsInProgressMessage": "Jobs are being processed in the background. Check back later for updates.", "regenerating": "Regenerating", "errorLoadingArticles": "Error loading articles"}, "excelImportReview": {"title": "Review Pending Import", "titleWithType": "Review Pending Import: {{recordType}}", "selectTypePlaceholder": "Select Type...", "sessionIdLabel": "Session ID:", "status": {"confirmSuccess": "Import for {{recordType}} confirmed successfully!", "confirmError": "Confirm Error for {{recordType}}: {{errorMessage}}", "discardSuccess": "Import for {{recordType}} discarded successfully!", "discardError": "Discard Error for {{recordType}}: {{errorMessage}}", "processingConfirm": "Processing confirmation for {{recordType}}...", "processingDiscard": "Processing discard for {{recordType}}...", "loadingTypesError": "Error loading session types: {{errorMessage}}", "loadingDataError": "Error loading data for {{recordType}}: {{errorMessage}}", "noTypesFoundForSession": "No pending data types found for this session.", "processedSuccess": "The import for '{{recordType}}' has been processed.", "previousErrorRetryHint": "An error occurred previously. You can try the action again."}, "loading": {"sessionTitle": "Loading Session...", "loadingTypes": "Loading available data types for this import session...", "loadingDataPage": "Loading data page..."}, "errors": {"unknownError": "Unknown error", "unknownErrorLoadingPending": "Unknown error loading pending data.", "noDataFoundTitle": "No Data Found", "noDataFoundText": "No pending data types were found for this import session, or there was an error loading them.", "invalidTypeTitle": "Review Pending Import: Invalid Type", "invalidTypeCardTitle": "Invalid Record Type", "invalidTypeText": "The record type '{{recordType}}' specified in the URL is not available in this import session.", "availableTypesLabel": "Available types:", "noPendingRecords": "No pending '{{recordType}}' records found for this session.", "failedToLoadData": "Failed to load data for {{recordType}}."}, "actions": {"backToDashboard": "Back to Dashboard", "retry": "Retry", "selectDataTypeTitle": "Select Data Type to Review", "confirmImport": "Confirm Import", "confirming": "Confirming...", "discardImport": "Discard Import", "discarding": "Discarding...", "commitModeLabel": "Select Confirmation Mode:", "commitModeReplace": "Replace", "commitModeReplaceWarning": "(Warning: Deletes ALL existing confirmed '{{recordType}}' records for your account!)", "title": "Actions for {{recordType}}"}, "preview": {"title": "Data Preview: {{recordType}} ({{count}} Records Pending)"}, "prompts": {"confirmImport": "Confirm import for {{recordType}} (Session: {{sessionId}}) using '{{commitMode}}' mode?", "confirmReplaceWarning": "\n\nWARNING: This will DELETE ALL existing confirmed '{{recordType}}' records for your account first!", "discardImport": "Discard pending import for {{recordType}} (Session: {{sessionId})? This cannot be undone."}}}